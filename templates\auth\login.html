{% extends "base.html" %}

{% block title %}Login - Devsphere.run{% endblock %}

{% block meta_description %}Sign in to your Devsphere.run account to access your profile, projects, and connect with the developer community.{% endblock %}

{% block content %}
<div class="max-w-md mx-auto bg-gray-900 rounded-2xl shadow-xl p-8">
  <div class="text-center mb-8">
    <h1 class="text-3xl font-bold text-electric mb-2">Welcome Back</h1>
    <p class="text-gray-400">Sign in to your account</p>
  </div>

  <form method="POST" novalidate class="space-y-6" id="login-form">
    <div>
      <label for="email" class="block text-sm font-medium text-gray-300 mb-2">
        Email Address
      </label>
      <input type="email" 
             id="email" 
             name="email" 
             required
             autocomplete="email"
             class="form-input w-full px-4 py-3 bg-gray-800 border border-gray-700 rounded-lg text-white placeholder-gray-400 focus-visible"
             placeholder="Enter your email"
             aria-describedby="email-error">
      <div id="email-error" class="text-red-400 text-sm mt-1 hidden" role="alert"></div>
    </div>

    <div>
      <label for="password" class="block text-sm font-medium text-gray-300 mb-2">
        Password
      </label>
      <div class="relative">
        <input type="password" 
               id="password" 
               name="password" 
               required
               autocomplete="current-password"
               class="form-input w-full px-4 py-3 bg-gray-800 border border-gray-700 rounded-lg text-white placeholder-gray-400 focus-visible pr-12"
               placeholder="Enter your password"
               aria-describedby="password-error">
        <button type="button" 
                id="toggle-password"
                class="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-300 focus-visible"
                aria-label="Toggle password visibility">
          <svg id="eye-icon" class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
          </svg>
        </button>
      </div>
      <div id="password-error" class="text-red-400 text-sm mt-1 hidden" role="alert"></div>
    </div>

    <div class="flex items-center justify-between">
      <div class="flex items-center">
        <input type="checkbox" 
               id="remember_me" 
               name="remember_me" 
               class="h-4 w-4 text-electric bg-gray-800 border-gray-600 rounded focus:ring-electric focus:ring-2">
        <label for="remember_me" class="ml-2 text-sm text-gray-300">
          Remember me
        </label>
      </div>
      <a href="{{ url_for('auth.forgot_password') }}" 
         class="text-sm text-electric hover:text-magenta transition-colors focus-visible">
        Forgot password?
      </a>
    </div>

    <button type="submit" 
            class="w-full py-3 px-4 bg-electric text-darkgray font-semibold rounded-lg shadow-lg
                   hover:bg-magenta hover:text-white transition-all transform hover:scale-105 focus-visible
                   disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none"
            id="submit-btn">
      <span id="submit-text">Sign In</span>
      <svg id="loading-spinner" class="hidden animate-spin h-5 w-5 mx-auto" fill="none" viewBox="0 0 24 24">
        <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
        <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
      </svg>
    </button>
  </form>

  <div class="mt-6 text-center">
    <p class="text-gray-400">
      Don't have an account? 
      <a href="{{ url_for('auth.register') }}" 
         class="text-electric hover:text-magenta transition-colors font-medium focus-visible">
        Sign up here
      </a>
    </p>
  </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
  const form = document.getElementById('login-form');
  const emailInput = document.getElementById('email');
  const passwordInput = document.getElementById('password');
  const togglePassword = document.getElementById('toggle-password');
  const eyeIcon = document.getElementById('eye-icon');
  const submitBtn = document.getElementById('submit-btn');
  const submitText = document.getElementById('submit-text');
  const loadingSpinner = document.getElementById('loading-spinner');

  // Email validation
  function validateEmail(email) {
    const re = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return re.test(email);
  }

  // Real-time validation
  emailInput.addEventListener('blur', function() {
    const email = this.value.trim();
    const errorDiv = document.getElementById('email-error');
    
    if (!email) {
      showError(this, errorDiv, 'Email is required');
    } else if (!validateEmail(email)) {
      showError(this, errorDiv, 'Please enter a valid email address');
    } else {
      clearError(this, errorDiv);
    }
  });

  passwordInput.addEventListener('blur', function() {
    const password = this.value;
    const errorDiv = document.getElementById('password-error');
    
    if (!password) {
      showError(this, errorDiv, 'Password is required');
    } else {
      clearError(this, errorDiv);
    }
  });

  // Password visibility toggle
  togglePassword.addEventListener('click', function() {
    const type = passwordInput.getAttribute('type') === 'password' ? 'text' : 'password';
    passwordInput.setAttribute('type', type);
    
    if (type === 'text') {
      eyeIcon.innerHTML = '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L3 3m6.878 6.878L21 21"></path>';
    } else {
      eyeIcon.innerHTML = '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>';
    }
  });

  // Form submission
  form.addEventListener('submit', function(e) {
    e.preventDefault();
    
    const email = emailInput.value.trim();
    const password = passwordInput.value;
    let isValid = true;

    // Validate email
    if (!email) {
      showError(emailInput, document.getElementById('email-error'), 'Email is required');
      isValid = false;
    } else if (!validateEmail(email)) {
      showError(emailInput, document.getElementById('email-error'), 'Please enter a valid email address');
      isValid = false;
    }

    // Validate password
    if (!password) {
      showError(passwordInput, document.getElementById('password-error'), 'Password is required');
      isValid = false;
    }

    if (isValid) {
      // Show loading state
      submitBtn.disabled = true;
      submitText.classList.add('hidden');
      loadingSpinner.classList.remove('hidden');
      
      // Submit form
      this.submit();
    }
  });

  function showError(input, errorDiv, message) {
    input.classList.add('form-error');
    errorDiv.textContent = message;
    errorDiv.classList.remove('hidden');
  }

  function clearError(input, errorDiv) {
    input.classList.remove('form-error');
    errorDiv.classList.add('hidden');
  }
});
</script>
{% endblock %}
