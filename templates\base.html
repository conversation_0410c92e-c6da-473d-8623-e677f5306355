<!DOCTYPE html>
<html lang="en" class="dark">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1" />
  <title>Devsphere.run</title>

  <script src="https://cdn.tailwindcss.com"></script>
  <script>
    tailwind.config = {
      darkMode: 'class',
      theme: {
        extend: {
          fontFamily: { sans: ['Inter','ui-sans-serif','system-ui'] },
          colors: { darkgray:'#121212', electric:'#00f5ff', magenta:'#ff2eff' },
          transitionProperty: { 'width-label': 'width, opacity' }
        }
      }
    }
  </script>
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;600;700&display=swap" rel="stylesheet">
</head>

<body class="bg-darkgray text-gray-200 font-sans min-h-screen flex">

  <!-- Mobile open button -->
  <button id="open-btn"
          class="fixed top-4 left-4 md:hidden p-2 rounded-lg bg-gray-800 text-electric z-50 hidden"
          aria-label="Open sidebar">❯</button>

  <!-- Sidebar -->
  <aside id="sidebar"
         class="group fixed md:relative top-0 left-0 h-screen bg-gray-950 shadow-xl
                transition-[width,transform] duration-300 ease-in-out
                w-64 md:translate-x-0 -translate-x-full md:-translate-x-0
                flex flex-col z-40 overflow-y-auto">

    <!-- Brand + collapse -->
    <div class="flex items-center justify-between h-14 px-4 border-b border-gray-800">
      <span class="label text-2xl font-bold text-electric truncate transition-opacity duration-300">
        Devsphere.run
      </span>
      <button id="collapse-btn" class="p-2 rounded-lg text-gray-400 hover:text-magenta focus:outline-none transition-transform duration-300">
        <svg id="chev" xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 transition-transform duration-300" fill="none"
             viewBox="0 0 24 24" stroke="currentColor" stroke-width="2">
          <path stroke-linecap="round" stroke-linejoin="round" d="M15 19l-7-7 7-7" />
        </svg>
      </button>
    </div>

    <!-- Nav -->
    <nav id="nav" class="p-3 space-y-1">
      <a href="#"
         class="navlink flex items-center gap-3 px-3 py-2 rounded-xl hover:bg-gray-900 hover:text-electric transition-all duration-300">
        <svg class="h-5 w-5 shrink-0" viewBox="0 0 24 24" fill="currentColor"><path d="M12 3l9 8h-3v9H6v-9H3l9-8z"/></svg>
        <span class="label transition-opacity duration-300">Home</span>
      </a>
      <a href="#"
         class="navlink flex items-center gap-3 px-3 py-2 rounded-xl hover:bg-gray-900 hover:text-electric transition-all duration-300">
        <svg class="h-5 w-5 shrink-0" viewBox="0 0 24 24" fill="currentColor"><path d="M12 12a5 5 0 100-10 5 5 0 000 10zm-7 9a7 7 0 0114 0v1H5v-1z"/></svg>
        <span class="label transition-opacity duration-300">Community</span>
      </a>
      <a href="#"
         class="navlink flex items-center gap-3 px-3 py-2 rounded-xl hover:bg-gray-900 hover:text-electric transition-all duration-300">
        <svg class="h-5 w-5 shrink-0" viewBox="0 0 24 24" fill="currentColor"><path d="M4 4h16v4H4V4zm0 6h10v4H4v-4zm0 6h16v4H4v-4z"/></svg>
        <span class="label transition-opacity duration-300">Projects</span>
      </a>
      <a href="#"
         class="navlink flex items-center gap-3 px-3 py-2 rounded-xl hover:bg-gray-900 hover:text-electric transition-all duration-300">
        <svg class="h-5 w-5 shrink-0" viewBox="0 0 24 24" fill="currentColor"><path d="M7 4h10v2H7V4zm-3 6h16v10H4V10zm2 2v6h12v-6H6z"/></svg>
        <span class="label transition-opacity duration-300">Events</span>
      </a>
    </nav>

    <div class="mt-auto p-4 text-xs text-gray-500">© 2025 Devsphere</div>
  </aside>

  <!-- Scrim for mobile -->
  <div id="scrim" class="fixed inset-0 bg-black/50 z-30 hidden md:hidden"></div>

  <!-- Content -->
  <main id="content" class="flex-1 p-8 transition-all duration-300 md:ml-64">
    {% block content %}{% endblock %}
  </main>

  <script>
    const mql = window.matchMedia('(min-width: 768px)');
    const sidebar = document.getElementById('sidebar');
    const content = document.getElementById('content');
    const collapseBtn = document.getElementById('collapse-btn');
    const openBtn = document.getElementById('open-btn');
    const scrim = document.getElementById('scrim');
    const labels = () => Array.from(document.querySelectorAll('#sidebar .label'));
    const chev = document.getElementById('chev');

    let collapsed = false; 
    let mobileOpen = false; 

    function setCollapsed(state) {
      collapsed = state;
      sidebar.classList.toggle('w-64', !collapsed);
      sidebar.classList.toggle('w-16', collapsed);

      labels().forEach(el => el.style.opacity = collapsed ? '0' : '1');
      labels().forEach(el => el.classList.toggle('hidden', collapsed));

      if (mql.matches) {
        content.classList.toggle('md:ml-64', !collapsed);
        content.classList.toggle('md:ml-16', collapsed);
      }

      chev.style.transform = collapsed ? 'rotate(180deg)' : 'rotate(0deg)';
    }

    function openMobile() {
      mobileOpen = true;
      sidebar.classList.remove('-translate-x-full');
      scrim.classList.remove('hidden');
      openBtn.classList.add('hidden');
    }
    function closeMobile() {
      mobileOpen = false;
      sidebar.classList.add('-translate-x-full');
      scrim.classList.add('hidden');
      openBtn.classList.remove('hidden');
    }

    function init() {
      if (mql.matches) {
        sidebar.classList.remove('-translate-x-full');
        scrim.classList.add('hidden');
        openBtn.classList.add('hidden');
        setCollapsed(false);
      } else {
        setCollapsed(false);
        closeMobile();
      }
    }
    init();

    collapseBtn.addEventListener('click', () => setCollapsed(!collapsed));
    openBtn.addEventListener('click', openMobile);
    scrim.addEventListener('click', closeMobile);
    window.addEventListener('keydown', (e) => { if (e.key === 'Escape' && mobileOpen) closeMobile(); });
    mql.addEventListener('change', init);
  </script>
</body>
</html>
